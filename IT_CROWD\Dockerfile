# Start with an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container at /app
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of your application code into the container at /app
# This includes app.py, utils.py, and the static and templates folders.
COPY . .

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Define the command to run your app using Gunicorn
# This is the command that will be executed when the container starts
CMD ["gunicorn", "--worker-class", "eventlet", "-w", "1", "--bind", "0.0.0.0:5000", "app:app"]