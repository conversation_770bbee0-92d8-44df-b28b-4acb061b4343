
import os
import platform
import requests
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
import re
import logging

import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Setup logging to see output in your console
if not logging.getLogger().hasHandlers():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_polygon_string(polygon_str: str) -> List[Dict[str, float]]:
    """Parses a space-separated string of 'lat,lon' coordinates into a list of dictionaries."""
    polygon_points = []
    if not polygon_str:
        return polygon_points
        
    coord_pairs = polygon_str.strip().split(' ')
    for pair in coord_pairs:
        try:
            lat_str, lon_str = pair.split(',')
            polygon_points.append({"latitude": float(lat_str), "longitude": float(lon_str)})
        except (ValueError, IndexError):
            logging.warning(f"Could not parse coordinate pair: '{pair}' in polygon string.")
            continue
    return polygon_points

def parse_cap_xml_content(xml_content: str, cap_url: str) -> Optional[Dict[str, Any]]:
    """
    Parses CAP XML content, now correctly handling XML namespaces to find all data.
    """
    try:
        if isinstance(xml_content, bytes):
            xml_content = xml_content.decode('utf-8', errors='ignore')

        # **THE FIX**: Register the namespace to correctly find elements like <cap:headline>
        ET.register_namespace('cap', "urn:oasis:names:tc:emergency:cap:1.2")
        root = ET.fromstring(xml_content)
        ns = {'cap': 'urn:oasis:names:tc:emergency:cap:1.2'}

        def find_text(element, path):
            el = element.find(path, ns)
            return el.text.strip() if el is not None and el.text else None

        def parse_cap_datetime(dt_str):
            if not dt_str: return None
            try:
                if dt_str.endswith('Z'): dt_str = dt_str[:-1] + '+00:00'
                dt_str = dt_str.replace(" -", "-").replace(" +", "+")
                return datetime.fromisoformat(dt_str)
            except ValueError:
                logging.warning(f"Could not parse datetime '{dt_str}' for {cap_url}.")
                return None
        
        # Find the <info> block, which contains the main alert details
        info_element = root.find('cap:info', ns)
        if info_element is None:
            logging.warning(f"No <info> block found in CAP file: {cap_url}")
            return None

        # Process ALL <area> elements within the <info> block
        all_areas = []
        area_elements = info_element.findall('cap:area', ns)
        logging.info(f"Found {len(area_elements)} <area> blocks in {cap_url}")
        
        for area_block in area_elements:
            area_desc = find_text(area_block, 'cap:areaDesc')
            polygon_str = find_text(area_block, 'cap:polygon')
            polygon_coords = parse_polygon_string(polygon_str)
            
            all_areas.append({
                "areaDesc": area_desc,
                "polygon": polygon_coords,
            })

        parsed_data = {
            "identifier": find_text(root, 'cap:identifier'),
            "sender": find_text(root, 'cap:sender'),
            "sent": parse_cap_datetime(find_text(root, 'cap:sent')),
            "status": find_text(root, 'cap:status'),
            "msgType": find_text(root, 'cap:msgType'),
            "scope": find_text(root, 'cap:scope'),
            "event": find_text(info_element, 'cap:event'),
            "headline": find_text(info_element, 'cap:headline'),
            "description": find_text(info_element, 'cap:description'),
            "cap_url": cap_url,
            "areas": all_areas
        }
        
        # Add a top-level lat/lon from the first point of the first polygon for easy access
        if parsed_data['areas'] and parsed_data['areas'][0]['polygon']:
            first_point = parsed_data['areas'][0]['polygon'][0]
            parsed_data['latitude'] = first_point.get('latitude')
            parsed_data['longitude'] = first_point.get('longitude')
        
        logging.info(f"Successfully parsed headline: {parsed_data['headline']}")
        return parsed_data

    except ET.ParseError as e:
        logging.error(f"XML ParseError for {cap_url}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error parsing CAP XML {cap_url}: {e}")
        return None

def get_ghana_meteo_alerts(
    disaster_keywords: List[str],
    rss_feed_url: str = "https://www.meteo.gov.gh/api/cap/rss.xml"
) -> List[Dict[str, Any]]:
    
    all_parsed_cap_alerts = []
    logging.info(f"Fetching GMet alerts from RSS feed: {rss_feed_url}")

    try:
        response = requests.get(rss_feed_url, timeout=20, verify=False)
        response.raise_for_status()
        rss_content = response.content
    except requests.RequestException as e:
        logging.error(f"Error fetching RSS feed {rss_feed_url}: {e}")
        return []

    try:
        root_rss = ET.fromstring(rss_content)
        channel = root_rss.find('channel')
        if channel is None: return []
        
        cap_xml_urls_from_rss = [link.text for item in channel.findall('item') if (link := item.find('link')) is not None and link.text]
        logging.info(f"Found {len(cap_xml_urls_from_rss)} alert links in RSS feed.")

    except ET.ParseError as e:
        logging.error(f"Error parsing RSS feed XML: {e}")
        return []

    for xml_url in cap_xml_urls_from_rss:
        logging.info(f"  Fetching individual CAP XML from: {xml_url}")
        try:
            cap_response = requests.get(xml_url, timeout=20, verify=False)
            cap_response.raise_for_status()
            
            xml_content = cap_response.content
            parsed_cap_data = parse_cap_xml_content(xml_content, xml_url)
            
            if parsed_cap_data:
                all_parsed_cap_alerts.append(parsed_cap_data)
            else:
                logging.warning(f"    Could not parse CAP XML content from {xml_url}")

        except Exception as e:
            logging.error(f"    Unexpected error processing CAP from {xml_url}: {e}")
            
    return all_parsed_cap_alerts

# This allows you to test this file directly
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    print("\n--- Testing Local XML File 'pageSoure.xml' ---")
    try:
        with open('pageSoure.xml', 'rb') as f:
            local_xml_content = f.read()
        
        alert = parse_cap_xml_content(local_xml_content, "local_file_test")
        if alert:
            print("\n--- PARSED ALERT DATA ---")
            print(f"Headline: {alert.get('headline')}")
            print(f"Description: {alert.get('description')}")
            print(f"Found {len(alert.get('areas', []))} areas:")
            for i, area in enumerate(alert.get('areas', [])):
                print(f"  - Area {i+1}: {area.get('areaDesc')}")
                polygon = area.get('polygon', [])
                print(f"    - Polygon has {len(polygon)} points.")
        else:
            print("Failed to parse the local XML file.")

    except FileNotFoundError:
        print("Error: 'pageSoure.xml' not found in the current directory. Skipping local test.")
    except Exception as e:
        print(f"An error occurred during local file test: {e}")