<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Communications Platform - Ghana</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
            background: #0a0a0f; 
            color: #e0e0e0; 
            overflow: hidden; height: 100vh; position: relative;
        }
        #canvas-container {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            z-index: 1; opacity: 0.7; 
        }
        .overlay {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            pointer-events: none; z-index: 10; display: flex; flex-direction: column;
        }
        .header { padding: 20px; text-align: center; z-index: 20; pointer-events: auto; }
        .header h1 {
            font-size: 2.2rem; font-weight: 400; letter-spacing: 2px; text-transform: uppercase;
            background: linear-gradient(90deg, #00b4d8, #0077b6); 
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 180, 216, 0.3); 
        }
        .info-cards-container {
            display: flex; justify-content: center; gap: 15px; 
            padding: 10px 20px; 
            z-index: 15; pointer-events: auto; flex-wrap: wrap; 
        }
        .info-card {
            background: rgba(30, 30, 45, 0.7); 
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1); 
            border-radius: 12px; 
            padding: 12px 15px; 
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            min-width: 180px; 
            text-align: left;
        }
        .info-card h4 {
            font-size: 0.85rem; color: #00b4d8; margin-bottom: 6px;
            font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;
        }
        .info-card .value { font-size: 1.7rem; font-weight: bold; color: #ffffff; margin-bottom: 4px; }
        .info-card .breakdown { font-size: 0.7rem; color: #adb5bd; line-height: 1.3; }
        .info-card .breakdown span { display: block; margin-top: 2px; }

        .glass-card {
            background: rgba(30, 30, 45, 0.7); backdrop-filter: blur(12px); 
            border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 15px;
            padding: 20px; 
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease; pointer-events: auto;
        }
        .glass-card:hover {
            background: rgba(40, 40, 55, 0.8); transform: translateY(-3px); 
            box-shadow: 0 12px 45px rgba(0, 0, 0, 0.4);
        }
        .data-sources {
            position: absolute; bottom: 20px; left: 20px; z-index: 15; 
            display: flex; flex-direction: column; gap: 10px; 
        }
        .source-button {
            display: flex; align-items: center; gap: 10px; padding: 10px 15px; 
            cursor: pointer; min-width: 240px; border: none; color: #e0e0e0;
            font-size: 0.9rem; font-weight: 500; transition: all 0.3s ease;
        }
        .source-button.active {
            background: rgba(0, 180, 216, 0.25); border: 1px solid rgba(0, 180, 216, 0.6);
            color: #ffffff;
        }
        .source-button.loading { cursor: wait; opacity: 0.7; }
        .source-button:disabled { opacity: 0.4; cursor: not-allowed; background: rgba(100, 100, 100, 0.2); }
        .source-icon {
            width: 32px; height: 32px; border-radius: 8px; display: flex;
            align-items: center; justify-content: center; font-size: 1.2rem; 
        }
        .icon-placeholder { font-style: normal; font-weight: bold; } 
        .cap-alert { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .meteo { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro { background: linear-gradient(135deg, #27ae60, #16a085); }
        .nadmo { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .fire { background: linear-gradient(135deg, #e67e22, #d35400); }
        .police { background: linear-gradient(135deg, #34495e, #2c3e50); }

        .advisory-panel {
            position: absolute; bottom: 20px; right: 20px; z-index: 15; 
            max-width: 400px; max-height: calc(100vh - 250px); overflow-y: auto; 
        }
        .advisory-panel::-webkit-scrollbar { width: 8px; }
        .advisory-panel::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.05); border-radius: 10px; }
        .advisory-panel::-webkit-scrollbar-thumb { background: rgba(0, 180, 216, 0.4); border-radius: 10px; }
        .advisory-panel::-webkit-scrollbar-thumb:hover { background: rgba(0, 180, 216, 0.6); }
        .advisory-card { margin-bottom: 15px; }
        #cap-input-area { padding: 20px; } 
        #cap-input-area textarea {
            width: 100%; min-height: 100px; padding: 10px; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2); background: rgba(10, 10, 15, 0.3); 
            color: #e0e0e0; font-family: monospace; font-size: 0.85rem; margin-bottom: 10px; resize: vertical;
        }
        #cap-input-area button {
            padding: 10px 15px; border-radius: 8px; border: 1px solid #00b4d8;
            background: rgba(0, 180, 216, 0.4); color: #ffffff; cursor: pointer;
            transition: background 0.3s ease; font-weight: 500;
        }
        #cap-input-area button:hover { background: rgba(0, 180, 216, 0.6); }
        .advisory-header { display: flex; align-items: center; gap: 10px; margin-bottom: 10px; }
        .advisory-icon {
            width: 38px; height: 38px; border-radius: 50%; display: flex;
            align-items: center; justify-content: center; font-size: 1.3rem; flex-shrink: 0;
        }
        .weather-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro-icon { background: linear-gradient(135deg, #27ae60, #16a085); }
        .advisory-content h3 { font-size: 1.1rem; margin-bottom: 6px; color: #00d4ff; font-weight: 500; }
        .advisory-content p, .advisory-content small { font-size: 0.85rem; line-height: 1.5; opacity: 0.9; word-wrap: break-word; }
        .advisory-content small { font-size: 0.75rem; display: block; margin-top: 6px; opacity: 0.7; }
        .status-bar { display: flex; justify-content: center; gap: 20px; padding: 8px 15px; margin-top: 5px; }
        .status-bar.glass-card { width: fit-content; margin-left: auto; margin-right: auto; }
        .status-item { text-align: center; }
        .status-value { font-size: 1.6rem; font-weight: bold; color: #00d4ff; }
        .status-label { font-size: 0.7rem; opacity: 0.7; text-transform: uppercase; letter-spacing: 0.5px; }
        .loading-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #0a0a0f;
            display: flex; align-items: center; justify-content: center; z-index: 1000; transition: opacity 0.5s ease;
        }
        .loading-spinner {
            width: 60px; height: 60px; border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid #00d4ff; border-radius: 50%; animation: spin 1s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .hidden { opacity: 0; pointer-events: none; }

        /* Process Animation Styles */
        .process-animation-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(10, 10, 15, 0.95); backdrop-filter: blur(10px);
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            z-index: 2000; opacity: 0; visibility: hidden; transition: all 0.3s ease;
        }
        .process-animation-overlay.active { opacity: 1; visibility: visible; }
        .process-animation-title { font-size: 1.5rem; color: #00d4ff; margin-bottom: 30px; text-align: center; letter-spacing: 2px; text-transform: uppercase; }
        .process-step { display: flex; align-items: center; gap: 20px; margin: 15px 0; opacity: 0; transform: translateY(30px); transition: all 0.6s ease; }
        .process-step.active { opacity: 1; transform: translateY(0); }
        .process-step.completed { opacity: 0.6; transform: translateY(-10px); }
        .process-icon { width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; position: relative; animation: float 2s ease-in-out infinite; }
        .process-icon.loading { animation: float 2s ease-in-out infinite, pulse 1.5s ease-in-out infinite; }
        .process-icon::after { content: ''; position: absolute; top: -5px; left: -5px; right: -5px; bottom: -5px; border: 2px solid transparent; border-radius: 50%; transition: all 0.3s ease; }
        .process-icon.loading::after { border-top: 2px solid rgba(0, 212, 255, 0.8); border-right: 2px solid rgba(0, 212, 255, 0.6); border-bottom: 2px solid rgba(0, 212, 255, 0.4); border-left: 2px solid rgba(0, 212, 255, 0.2); animation: spin 1s linear infinite; }
        .process-icon.completed::after { border: 2px solid #27ae60; }
        .process-text { color: #fff; font-size: 1.1rem; font-weight: 300; letter-spacing: 1px; min-width: 300px; }
        .process-text.loading { color: #00d4ff; }
        .process-text.completed { color: #27ae60; }
        .icon-api { background: linear-gradient(135deg, #3498db, #2980b9); }
        .icon-scrape { background: linear-gradient(135deg, #e67e22, #d35400); }
        .icon-ai { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .icon-process { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .icon-format { background: linear-gradient(135deg, #27ae60, #16a085); }
        .icon-complete { background: linear-gradient(135deg, #2ecc71, #27ae60); }

        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }

        /* Map Test Controls */
        #map-test-controls {
            position: absolute;
            top: 170px; 
            right: 20px;
            background: rgba(30, 30, 45, 0.8);
            backdrop-filter: blur(8px);
            padding: 15px;
            border-radius: 10px;
            z-index: 20;
            pointer-events: auto;
            display: flex;
            flex-direction: column;
            gap: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        #map-test-controls label { font-size: 0.8rem; margin-bottom: 2px; color: #00d4ff;}
        #map-test-controls input {
            padding: 6px; border-radius: 5px; border: 1px solid rgba(255,255,255,0.2);
            background: rgba(10,10,15,0.5); color: #e0e0e0; font-size: 0.8rem; width: 150px;
        }
        #map-test-controls button {
            padding: 8px 12px; border-radius: 5px; border: none;
            background: #0099cc; color: white; cursor: pointer; font-size: 0.8rem;
            transition: background 0.3s ease;
        }
        #map-test-controls button:hover { background: #00b4d8; }

    </style>
</head>
<body>
    <div class="loading-overlay" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <div class="process-animation-overlay" id="process-animation">
        <div class="process-animation-title" id="process-title">Fetching Data</div>
        <div id="process-steps-container"></div>
    </div>

    <div id="canvas-container"></div> 
    <div class="overlay">
        <div class="header">
            <h1>Emergency Communications Platform</h1>
        </div>

        <div class="info-cards-container">
            <div class="info-card">
                <h4>CAP Alerts by Source</h4>
                <div class="value" id="cap-alerts-total">0</div>
                <div class="breakdown">
                    <span id="cap-manual-count">Manual: 0</span>
                    <span id="cap-gmet-count">GMet: 0</span> 
                    <span id="cap-agro-count">Agro API: 0</span>
                </div>
            </div>
            <div class="info-card">
                <h4>Messages Forwarded</h4>
                <div class="value" id="messages-forwarded-total">0</div>
                <div class="breakdown">
                    <span id="sms-forwarded-count">SMS: 0</span>
                    <span id="voice-forwarded-count">Voice: 0</span>
                    <span id="pa-lora-forwarded-count">Digital PA (LoRa): 0</span>
                </div>
            </div>
            <div class="info-card">
                <h4>System Status</h4>
                <div class="value" id="system-status-value" style="font-size: 1.2rem; color: #27ae60;">All Systems Operational</div>
                <div class="breakdown">
                    <span id="api-status">APIs: Online</span>
                    <span id="lora-link-status">LoRa Link: Active</span> 
                </div>
            </div>
            <div class="info-card">
                <h4>Active Critical Incidents</h4>
                <div class="value" id="critical-incidents-total">0</div>
                <div class="breakdown">
                    <span id="severe-weather-alerts">Severe Weather: 0</span>
                    <span id="high-priority-cap">High Priority CAP: 0</span>
                </div>
            </div>
        </div>

        <div id="map-test-controls">
            <label for="test-lat">Latitude:</label>
            <input type="number" id="test-lat" value="5.56" step="0.01">
            <label for="test-lon">Longitude:</label>
            <input type="number" id="test-lon" value="-0.20" step="0.01">
            <label for="test-event">Event Type:</label>
            <input type="text" id="test-event" value="fire">
            <button id="test-focus-map">Focus Map on Test Event</button>
        </div>

        <div class="status-bar glass-card"> 
            <div class="status-item">
                <div class="status-value" id="active-alerts-legacy">0</div> 
                <div class="status-label">Active Alerts (Overall)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="coverage">0</div>
                <div class="status-label">Coverage Area (km²)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="response-time">--</div>
                <div class="status-label">Avg. Response Time</div>
            </div>
        </div>

        <div class="data-sources">
            <button class="source-button glass-card" data-source="gmet_cap" id="btn-gmet-cap">
                <div class="source-icon cap-alert"><span class="icon-placeholder">GMA</span></div>
                <span>GMet CAP Alerts</span>
            </button>
            <button class="source-button glass-card" data-source="manual_cap" id="btn-manual-cap">
                <div class="source-icon cap-alert"><span class="icon-placeholder">EDT</span></div>
                <span>Manual CAP Input</span>
            </button>
            <button class="source-button glass-card" data-source="meteo" id="btn-meteo">
                <div class="source-icon meteo"><span class="icon-placeholder">MET</span></div>
                <span>Open Meteo API</span>
            </button>
            <button class="source-button glass-card" data-source="agro" id="btn-agro">
                <div class="source-icon agro"><span class="icon-placeholder">AGR</span></div>
                <span>GHAAP Agro-Climate</span>
            </button>
            <button class="source-button glass-card" data-source="nadmo" id="btn-nadmo" disabled>
                <div class="source-icon nadmo"><span class="icon-placeholder">NAD</span></div>
                <span>NADMO (Future)</span>
            </button>
            <button class="source-button glass-card" data-source="fire" id="btn-fire" disabled>
                <div class="source-icon fire"><span class="icon-placeholder">FIR</span></div>
                <span>Fire Service (Future)</span>
            </button>
            <button class="source-button glass-card" data-source="police" id="btn-police" disabled>
                <div class="source-icon police"><span class="icon-placeholder">POL</span></div>
                <span>Ghana Police (Future)</span>
            </button>
        </div>

        <div class="advisory-panel">
            <div class="advisory-card glass-card" id="cap-input-area" style="display: none;">
                 <div class="advisory-header">
                    <div class="advisory-icon cap-alert"><span class="icon-placeholder">EDT</span></div>
                    <div class="advisory-content">
                        <h3>Input CAP XML</h3>
                    </div>
                </div>
                <textarea id="cap-xml-input" placeholder="Paste CAP XML here..."></textarea>
                <button id="submit-cap-xml">Submit CAP Alert</button>
            </div>

            <div class="advisory-card glass-card" id="weather-advisory" style="display: none;">
                <div class="advisory-header">
                    <div class="advisory-icon weather-icon"><span class="icon-placeholder">WTH</span></div> 
                    <div class="advisory-content">
                        <h3>Weather Advisory</h3>
                        <p id="weather-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
            <div class="advisory-card glass-card" id="agro-advisory" style="display: none;">
                <div class="advisory-header">
                    <div class="advisory-icon agro-icon"><span class="icon-placeholder">AGR</span></div>
                    <div class="advisory-content">
                        <h3>Agro-Climate Advisory</h3>
                        <p id="agro-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
             <div class="advisory-card glass-card" id="cap-alert-display" style="display: none;">
                <div class="advisory-header">
                    <div class="advisory-icon cap-alert"><span class="icon-placeholder">CAP</span></div>
                    <div class="advisory-content">
                        <h3 id="cap-headline">CAP Alert</h3>
                        <p id="cap-description">Awaiting data...</p>
                        <small id="cap-area">Area: Not specified</small>
                        <small id="cap-sender" style="margin-top: 5px;">Sender: Not specified</small>
                        <small id="cap-sent-time" style="margin-top: 5px;">Sent: Not specified</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
<script>
    // --- Part 1: Core Classes, Global Variables, and Socket.IO Setup ---

        // --- Process Animation System Definition ---
        class ProcessAnimationManager {
            constructor() {
                this.overlay = document.getElementById('process-animation');
                this.title = document.getElementById('process-title');
                this.container = document.getElementById('process-steps-container');
                this.currentSteps = [];
                this.currentStepIndex = 0;
                this.stepDelay = 1000; 
            }
            getProcessSequence(source) {
                const sequences = { 
                    'meteo': [
                        { icon: '🌐', iconClass: 'icon-api', text: 'Connecting to Open Meteo API...' },
                        { icon: '📍', iconClass: 'icon-process', text: 'Getting location coordinates...' },
                        { icon: '🌤️', iconClass: 'icon-api', text: 'Fetching weather forecast data...' },
                        { icon: '🤖', iconClass: 'icon-ai', text: 'Processing with AI summarization...' },
                        { icon: '📝', iconClass: 'icon-format', text: 'Formatting weather advisory...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'Weather data ready!' }
                    ],
                    'agro': [
                        { icon: '🌐', iconClass: 'icon-api', text: 'Connecting to GHAAP.com...' },
                        { icon: '🔍', iconClass: 'icon-scrape', text: 'Attempting AI-powered scraping...' },
                        { icon: '🤖', iconClass: 'icon-ai', text: 'Using ScrapeGraphAI with OpenAI...' },
                        { icon: '🚗', iconClass: 'icon-scrape', text: 'Trying Selenium browser automation...' },
                        { icon: '🎯', iconClass: 'icon-process', text: 'Looking for element ID="wt_smssend"...' },
                        { icon: '📊', iconClass: 'icon-process', text: 'Extracting agricultural data...' },
                        { icon: '🌾', iconClass: 'icon-format', text: 'Processing crop advisory...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'Agro-climate data ready!' }
                    ],
                    'cap': [ // For manually submitted CAP
                        { icon: '📄', iconClass: 'icon-process', text: 'Parsing CAP XML data...' },
                        { icon: '🔍', iconClass: 'icon-ai', text: 'Validating alert structure...' },
                        { icon: '📍', iconClass: 'icon-process', text: 'Processing geographic areas...' },
                        { icon: '⚡', iconClass: 'icon-format', text: 'Formatting emergency alert...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'CAP alert processed!' }
                    ],
                    'gmet_cap': [ // Updated for RSS feed process
                        { icon: '📡', iconClass: 'icon-api', text: 'Initiating GMet RSS feed fetch...' },    // Step 0
                        { icon: '🔍', iconClass: 'icon-scrape', text: 'Fetching RSS & linked CAP data...' }, // Step 1
                        { icon: '📄', iconClass: 'icon-process', text: 'Processing retrieved alerts...' },   // Step 2
                        { icon: '🇬🇭', iconClass: 'icon-format', text: 'Finalizing GMet alert display...' },  // Step 3
                        { icon: '✅', iconClass: 'icon-complete', text: 'GMet alert ready!' }             // Step 'complete'
                    ]
                };
                return sequences[source] || [
                    { icon: '🔄', iconClass: 'icon-process', text: 'Processing request...' },
                    { icon: '✅', iconClass: 'icon-complete', text: 'Request completed!' }
                ];
            }
            show(source, title = null) {
                this.currentSteps = this.getProcessSequence(source); 
                this.currentStepIndex = 0;
                if (this.title) this.title.textContent = title || `Fetching ${source.toUpperCase()} Data`;
                if (this.container) this.container.innerHTML = '';
                
                this.currentSteps.forEach((step) => {
                    const stepElement = document.createElement('div'); 
                    stepElement.className = 'process-step';
                    stepElement.innerHTML = `<div class="process-icon ${step.iconClass}">${step.icon}</div><div class="process-text">${step.text}</div>`;
                    if (this.container) this.container.appendChild(stepElement);
                });
                if (this.overlay) this.overlay.classList.add('active');
                this.showStep(0); 
            }
            hide() { 
                if (this.overlay) this.overlay.classList.remove('active'); 
                setTimeout(() => { 
                    if (this.container) this.container.innerHTML = ''; 
                    this.currentSteps = []; 
                    this.currentStepIndex = 0; 
                }, 300); 
            }
            showStep(stepIndex) { 
                if (this.container && stepIndex < this.container.children.length) {
                    for (let i = 0; i < stepIndex; i++) {
                        const prevStep = this.container.children[i];
                        if (prevStep && !prevStep.classList.contains('completed')) {
                            const prevIcon = prevStep.querySelector('.process-icon'); 
                            const prevText = prevStep.querySelector('.process-text');
                            prevStep.classList.add('completed'); 
                            if (prevIcon) { prevIcon.classList.remove('loading'); prevIcon.classList.add('completed');}
                            if (prevText) { prevText.classList.remove('loading'); prevText.classList.add('completed');}
                        }
                    }
                    const stepElement = this.container.children[stepIndex];
                    if (stepElement) {
                        const icon = stepElement.querySelector('.process-icon'); 
                        const text = stepElement.querySelector('.process-text');
                        stepElement.classList.add('active'); 
                        if (icon) icon.classList.add('loading'); 
                        if (text) text.classList.add('loading');
                        this.currentStepIndex = stepIndex + 1;
                    }
                }
            }
            updateStep(stepIndex, newText) { 
                 if (this.container && stepIndex < this.container.children.length) {
                    const stepElement = this.container.children[stepIndex];
                    if (stepElement) {
                        const textElement = stepElement.querySelector('.process-text');
                        if (textElement) textElement.textContent = newText;
                    }
                }
            }
            complete() { 
                if (this.container) {
                    for (let i = 0; i < this.container.children.length; i++) {
                        const step = this.container.children[i];
                        if (step) {
                            const icon = step.querySelector('.process-icon'); 
                            const text = step.querySelector('.process-text');
                            step.classList.add('completed'); 
                            if(icon) {icon.classList.remove('loading'); icon.classList.add('completed');}
                            if(text) {text.classList.remove('loading'); text.classList.add('completed');}
                        }
                    }
                }
                setTimeout(() => this.hide(), 1500);
            }
            setError(errorText) {
                 if (this.container && this.currentStepIndex > 0 && this.container.children.length > (this.currentStepIndex -1) ) { 
                    const currentStep = this.container.children[this.currentStepIndex - 1];
                    if (currentStep) { 
                        const icon = currentStep.querySelector('.process-icon');
                        const text = currentStep.querySelector('.process-text');
                        if (icon && text) { 
                             icon.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                             icon.textContent = '❌';
                             icon.classList.remove('loading');
                             text.textContent = errorText;
                             text.style.color = '#e74c3c';
                             text.classList.remove('loading');
                        }
                    }
                }
                setTimeout(() => this.hide(), 2000);
            }
        }

        // --- Global Scope Variable Declarations ---
        const processAnimation = new ProcessAnimationManager(); 
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        
        const GEOJSON_FILE_PATH = "{{ url_for('static', filename='ghana_regions.json') }}"; 
        
        let ghanaMapGroup; 
        let mapCenter = { x: 0, y: 0 }; 
        let mapScaleFactor = 1;       
        let incidentMarker = null; 
        let activeIncidentAnimation = null; 
        let currentWeatherEffect = null; 

        let socket; 
        let currentSessionId = null;

        // --- Socket.IO Initialization and Event Handlers ---
        console.log("Script start: Checking for io object...");
        if (typeof io === 'undefined') {
            console.error("Socket.IO client library (io) is NOT LOADED. Ensure the script tag for socket.io.js is correct and the file is accessible.");
            alert("Error: Real-time communication library failed to load. Progress updates will not work.");
        } else {
            console.log("io object found. Attempting to initialize socket connection to /ui_client");
            try {
                socket = io('/ui_client'); 
                console.log("Socket initialization attempted. Check browser console for connection status or errors.");

                socket.on('connect', () => {
                    console.log('Socket.IO: Connected to Flask-SocketIO server for UI updates. SID:', socket.id);
                });

                socket.on('progress_update', (data) => {
                    if (currentSessionId && data.session_id === currentSessionId) {
                        console.log('SOCKETIO progress_update received:', data); 
                        if (data.step === 'complete') {
                            processAnimation.complete();
                        } else if (data.step === 'error') {
                            processAnimation.setError(data.message);
                        } else if (typeof data.step === 'number' && data.message != null) {
                            processAnimation.showStep(data.step); 
                            processAnimation.updateStep(data.step, data.message); 
                        }
                    } else if (!currentSessionId && data.session_id) {
                         console.warn('SocketIO: Progress update received, but no currentSessionId is set on the client for this source button click yet. SID of update:', data.session_id);
                    } else if (currentSessionId && data.session_id !== currentSessionId) {
                         console.warn('SocketIO: Progress update received for a different session. Current:', currentSessionId, 'Received:', data.session_id);
                    }
                });

                socket.on('disconnect', () => {
                    console.log('Socket.IO: Disconnected from Flask-SocketIO server.');
                });
                
                socket.on('connect_error', (err) => {
                    console.error('Socket.IO: Connection Error!', err);
                    alert('Failed to connect to the real-time update server. Please check the server status and your network connection.');
                });

            } catch (e) {
                console.error("Error during socket = io('/ui_client') initialization:", e);
                alert("Failed to initialize real-time communication. Progress updates may not work.");
            }
        }
        // --- End of Part 1 ---


        // --- Part 2: Three.js Scene, Map, and Helper Functions ---

        // Note: scene, camera, renderer are already declared globally in Part 1.
        // Initial Three.js renderer setup will be completed in DOMContentLoaded (Part 4)
        // by appending renderer.domElement to the canvas-container.
        
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7); 
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9);
        directionalLight.position.set(15, 20, 10); 
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048; 
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        scene.add(directionalLight);
        
        async function loadAndCreateGhanaMap() {
            let geoJsonPath;
            try {
                geoJsonPath = GEOJSON_FILE_PATH;
                console.log(`Attempting to fetch GeoJSON from: ${geoJsonPath}`);
                const response = await fetch(geoJsonPath);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} when fetching ${geoJsonPath}`);
                }
                const geoJsonData = await response.json();
                createGhanaMapFromGeoJSON(geoJsonData);
            } catch (error) {
                console.error(`Could not load or parse GeoJSON data from ${geoJsonPath || GEOJSON_FILE_PATH}:`, error);
                createFallbackMap();
            }
        }

        function createFallbackMap() { 
            console.warn("Creating fallback map due to GeoJSON loading error.");
            ghanaMapGroup = new THREE.Group(); 
            const fallbackMaterial = new THREE.MeshPhongMaterial({ color: 0x555555 });
            const fallbackGeom = new THREE.BoxGeometry(8,0.5,8);
            const fallbackMesh = new THREE.Mesh(fallbackGeom, fallbackMaterial);
            fallbackMesh.position.y = -0.5;
            ghanaMapGroup.add(fallbackMesh);
            scene.add(ghanaMapGroup);
        }

        function createGhanaMapFromGeoJSON(geoJsonData) {
            if (!geoJsonData || !geoJsonData.features || geoJsonData.features.length === 0) {
                console.error("GeoJSON data is empty or invalid."); createFallbackMap(); return;
            }
            ghanaMapGroup = new THREE.Group();
            const defaultRegionMaterial = new THREE.MeshPhongMaterial({ color: 0x006A4E, shininess: 15, side: THREE.DoubleSide });
            const outlineMaterial = new THREE.LineBasicMaterial({ color: 0xFCD116, linewidth: 2 });
            
            const allPoints = [];
            geoJsonData.features.forEach(feature => { 
                 if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon' ? [feature.geometry.coordinates] : feature.geometry.coordinates;
                    polygons.forEach(polygonCoordsArray => {
                        const exteriorRing = polygonCoordsArray[0];
                        if (exteriorRing && exteriorRing.length > 0) {
                            exteriorRing.forEach(coord => {
                                if (coord && typeof coord[0] === 'number' && typeof coord[1] === 'number') {
                                    allPoints.push(new THREE.Vector2(coord[0], coord[1]));
                                } else { console.warn("Invalid coordinate in GeoJSON:", coord, "Feature:", feature.properties.region); }
                            });
                        }
                    });
                }
            });
            if (allPoints.length === 0) { console.error("No valid coordinates for map."); createFallbackMap(); return; }

            const boundingBox = new THREE.Box2().setFromPoints(allPoints);
            mapCenter = boundingBox.getCenter(new THREE.Vector2()); 
            const size = boundingBox.getSize(new THREE.Vector2());
            const maxDim = Math.max(size.x, size.y);
            mapScaleFactor = maxDim > 0 ? (15 / maxDim) : 1; 

            geoJsonData.features.forEach(feature => { 
                if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon' ? [feature.geometry.coordinates] : feature.geometry.coordinates;
                    polygons.forEach((polygonCoordsArray, polyIndex) => {
                        const exteriorRingCoords = polygonCoordsArray[0];
                        if (!exteriorRingCoords || exteriorRingCoords.length < 3) { console.warn("Skipping invalid exterior ring:", feature.properties.region, polyIndex); return; }
                        const regionShape = new THREE.Shape();
                        const firstPoint = exteriorRingCoords[0];
                        if (!firstPoint || typeof firstPoint[0] !== 'number' || typeof firstPoint[1] !== 'number') { console.warn("Invalid first point for region:", feature.properties.region); return; }
                        regionShape.moveTo((firstPoint[0] - mapCenter.x) * mapScaleFactor, (firstPoint[1] - mapCenter.y) * mapScaleFactor);
                        for (let i = 1; i < exteriorRingCoords.length; i++) {
                            const point = exteriorRingCoords[i];
                            if (!point || typeof point[0] !== 'number' || typeof point[1] !== 'number') { console.warn("Skipping invalid coordinate for region:", feature.properties.region); continue; }
                            regionShape.lineTo((point[0] - mapCenter.x) * mapScaleFactor, (point[1] - mapCenter.y) * mapScaleFactor);
                        }
                        for (let h = 1; h < polygonCoordsArray.length; h++) { 
                            const holeCoords = polygonCoordsArray[h]; if (!holeCoords || holeCoords.length < 3) continue;
                            const holePath = new THREE.Path(); const firstHolePoint = holeCoords[0];
                            if (!firstHolePoint || typeof firstHolePoint[0] !== 'number' || typeof firstHolePoint[1] !== 'number') continue;
                            holePath.moveTo((firstHolePoint[0] - mapCenter.x) * mapScaleFactor, (firstHolePoint[1] - mapCenter.y) * mapScaleFactor);
                            for (let i = 1; i < holeCoords.length; i++) {
                                const holePoint = holeCoords[i]; if (!holePoint || typeof holePoint[0] !== 'number' || typeof holePoint[1] !== 'number') continue;
                                holePath.lineTo((holePoint[0] - mapCenter.x) * mapScaleFactor, (holePoint[1] - mapCenter.y) * mapScaleFactor);
                            } regionShape.holes.push(holePath);
                        }
                        const extrudeSettings = { steps: 1, depth: 0.3, bevelEnabled: true, bevelThickness: 0.05, bevelSize: 0.05, bevelOffset: 0, bevelSegments: 1 };
                        const geometry = new THREE.ExtrudeGeometry(regionShape, extrudeSettings);
                        const regionMesh = new THREE.Mesh(geometry, defaultRegionMaterial.clone()); 
                        regionMesh.castShadow = true; regionMesh.receiveShadow = true; 
                        regionMesh.userData = { region: feature.properties.region, originalMaterial: defaultRegionMaterial.clone() }; 
                        ghanaMapGroup.add(regionMesh);
                        const edgesGeom = new THREE.EdgesGeometry(geometry);
                        const regionOutline = new THREE.LineSegments(edgesGeom, outlineMaterial.clone());
                        ghanaMapGroup.add(regionOutline);
                    });
                }
            });
            ghanaMapGroup.rotation.x = -Math.PI / 2; 
            ghanaMapGroup.position.y = -0.5; 
            scene.add(ghanaMapGroup);
            console.log("Ghana map created. Regions:", ghanaMapGroup.children.filter(c => c.type === 'Mesh').length);
        }
        
        function createRainEffect() { 
            const rainGroup = new THREE.Group(); const rainCount = 200;
            for (let i = 0; i < rainCount; i++) {
                const geometry = new THREE.BufferGeometry(); const positions = new Float32Array(6);
                const x = (Math.random() - 0.5) * 20; const z = (Math.random() - 0.5) * 20; const y = Math.random() * 15;
                positions[0] = x; positions[1] = y; positions[2] = z; positions[3] = x; positions[4] = y - 0.5; positions[5] = z;
                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                const material = new THREE.LineBasicMaterial({ color: 0x4a90e2, transparent: true, opacity: 0.6, linewidth: 1 });
                const line = new THREE.Line(geometry, material); rainGroup.add(line);
            } return rainGroup;
        }
        function createSunEffect() { 
            const sunGroup = new THREE.Group();
            const sunGeometry = new THREE.SphereGeometry(1, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({ color: 0xffdd00, emissive: 0xffdd00, emissiveIntensity: 0.5 });
            const sun = new THREE.Mesh(sunGeometry, sunMaterial); sun.position.set(5, 8, -5); sunGroup.add(sun);
            const rayGeometry = new THREE.ConeGeometry(0.1, 5, 4);
            const rayMaterial = new THREE.MeshBasicMaterial({ color: 0xffdd00, transparent: true, opacity: 0.3 });
            const rays = new THREE.Group();
            for (let i = 0; i < 8; i++) {
                const ray = new THREE.Mesh(rayGeometry, rayMaterial); ray.position.copy(sun.position); ray.rotation.z = (Math.PI * 2 / 8) * i; rays.add(ray);
            } sunGroup.add(rays); return sunGroup;
        }

        function geoToSceneCoordinates(longitude, latitude) {
            if (!ghanaMapGroup || mapCenter.x === undefined || mapScaleFactor === undefined) {
                console.warn("Map data not ready for coordinate conversion."); return null; 
            }
            const sceneX = (longitude - mapCenter.x) * mapScaleFactor;
            const sceneZ = -(latitude - mapCenter.y) * mapScaleFactor; 
            const sceneY = ghanaMapGroup.position.y + 0.15; // Small offset above the map plane
            return new THREE.Vector3(sceneX, sceneY, sceneZ);
        }

        function focusMapOnCAPIncident(alertData, zoomFactor = 0.3) {
            if (!ghanaMapGroup || !camera) {
                console.error("Three.js map or camera not initialized.");
                return;
            }

            // Clear previous incident visuals, including multiple polygons
            if (activeIncidentAnimation) {
                scene.remove(activeIncidentAnimation);
                activeIncidentAnimation = null;
            }
            if (incidentMarker) {
                scene.remove(incidentMarker);
                gsap.killTweensOf(incidentMarker.scale);
                incidentMarker = null;
            }

            const eventType = alertData.event ? alertData.event.toLowerCase() : "default";
            let firstFocusPoint = null;

            // --- New: Use a Group to hold all polygon meshes ---
            activeIncidentAnimation = new THREE.Group();
            scene.add(activeIncidentAnimation);

            // --- Key Change: Loop through the 'areas' array ---
            if (alertData.areas && alertData.areas.length > 0) {
                alertData.areas.forEach((area, areaIndex) => {
                    if (area.polygon && area.polygon.length > 2) {
                        const polygonShape = new THREE.Shape();
                        let localFocusPoint = null;

                        area.polygon.forEach((point, pointIndex) => {
                            const scenePoint = geoToSceneCoordinates(point.longitude, point.latitude);
                            if (scenePoint) {
                                if (pointIndex === 0) {
                                    polygonShape.moveTo(scenePoint.x, scenePoint.z);
                                    localFocusPoint = scenePoint;
                                    // Set the camera focus target to the center of the first polygon
                                    if (areaIndex === 0) {
                                        firstFocusPoint = scenePoint;
                                    }
                                } else {
                                    polygonShape.lineTo(scenePoint.x, scenePoint.z);
                                }
                            }
                        });

                        if (localFocusPoint) {
                            const polygonMaterial = new THREE.MeshPhongMaterial({
                                color: 0xff4500,
                                emissive: 0xcc2200,
                                side: THREE.DoubleSide,
                                transparent: true,
                                opacity: 0.4
                            });
                            const extrudeSettings = { depth: 0.1, bevelEnabled: false };
                            const polygonGeometry = new THREE.ExtrudeGeometry(polygonShape, extrudeSettings);
                            
                            const polygonMesh = new THREE.Mesh(polygonGeometry, polygonMaterial);
                            polygonMesh.rotation.x = Math.PI / 2;
                            polygonMesh.position.y = ghanaMapGroup.position.y + 0.1 + (areaIndex * 0.01); // Slight offset for multiple areas
                            
                            activeIncidentAnimation.add(polygonMesh); // Add mesh to the group
                        }
                    }
                });
                console.log(`Map focused on CAP incident with ${alertData.areas.length} area(s).`);
            } else if (typeof alertData.longitude === 'number' && typeof alertData.latitude === 'number') {
                // Fallback to single point if 'areas' array is missing
                firstFocusPoint = geoToSceneCoordinates(alertData.longitude, alertData.latitude);
                if (firstFocusPoint) {
                    const markerGeometry = new THREE.SphereGeometry(0.15, 16, 16);
                    const markerMaterial = new THREE.MeshPhongMaterial({ color: 0xffd700, emissive: 0xccac00 });
                    incidentMarker = new THREE.Mesh(markerGeometry, markerMaterial);
                    incidentMarker.position.copy(firstFocusPoint);
                    scene.add(incidentMarker);
                    gsap.to(incidentMarker.scale, { x: 1.5, y: 1.5, z: 1.5, duration: 0.7, repeat: -1, yoyo: true, ease: "power1.inOut" });
                }
            }

            // Animate camera only if a valid focus point was determined
            if (firstFocusPoint) {
                const newCamPos = new THREE.Vector3(firstFocusPoint.x, firstFocusPoint.y + 12 * zoomFactor, firstFocusPoint.z + 6 * zoomFactor);
                gsap.to(camera.position, {
                    x: newCamPos.x,
                    y: newCamPos.y,
                    z: newCamPos.z,
                    duration: 1.5,
                    ease: "power2.inOut",
                    onUpdate: () => {
                        camera.lookAt(firstFocusPoint);
                    }
                });
            } else {
                console.warn("CAP data has no valid coordinates or polygons to focus on.");
            }
        }

        // --- End of Part 2 ---

        // --- Part 3: UI Helper Functions, Main Animation Loop, and General Utilities ---
        
        // Note: UI element variables (sourceButtons, capXmlInput, etc.) will be declared 
        // and initialized in Part 4, inside DOMContentLoaded.
        // Functions here will rely on those global-like variables being set by then.

        function showLoadingState(button, panelId, isLoading = true) {
            const textElement = panelId ? document.getElementById(panelId)?.querySelector('p, #cap-description') : null;
            const headlineElement = panelId ? document.getElementById(panelId)?.querySelector('h3#cap-headline') : null;
            if (button) {
                if (isLoading) button.classList.add('loading');
                else button.classList.remove('loading');
            }
            if (textElement) textElement.textContent = isLoading ? 'Fetching data...' : 'Awaiting data...';
            if (headlineElement) headlineElement.textContent = isLoading ? 'Processing...' : 'CAP Alert';
        }
        
        function updateAdvisoryUI(sourceKey, data) { 
            console.log("Updating UI for:", sourceKey, "with data:", data);
            
            // Ensure these element variables are defined (they will be in DOMContentLoaded)
            const weatherAdvEl = document.getElementById('weather-advisory');
            const agroAdvEl = document.getElementById('agro-advisory');
            const capDispEl = document.getElementById('cap-alert-display');
            const capInArea = document.getElementById('cap-input-area');
            // Add this for the GMet specific card if it exists in your HTML structure (from Untitled-2.html)
            const ghanaMeteoAdvisoryEl = document.getElementById('ghana-meteo-advisory');


            if(weatherAdvEl) weatherAdvEl.style.display = 'none'; 
            if(agroAdvEl) agroAdvEl.style.display = 'none';
            if(capDispEl) capDispEl.style.display = 'none'; 
            if(capInArea) capInArea.style.display = 'none'; 
            if(ghanaMeteoAdvisoryEl) ghanaMeteoAdvisoryEl.style.display = 'none';
            
            if (incidentMarker && sourceKey !== 'meteo' && sourceKey !== 'gmet_cap' && sourceKey !== 'cap') {
                scene.remove(incidentMarker);
                gsap.killTweensOf(incidentMarker.scale); 
                incidentMarker = null;
            }
            // Clear general weather effect unless it's a meteo update that's not an error
            if (currentWeatherEffect && (sourceKey !== 'meteo' || (data && data.error && sourceKey === 'meteo'))) {
                scene.remove(currentWeatherEffect);
                currentWeatherEffect = null;
            }

            if (data && data.error) {
                console.error("Error from backend for source", sourceKey, ":", data.error);
                let errorText = data.advisory_text || data.error || "An unknown error occurred.";
                const weatherIconDisplayEl = weatherAdvEl ? weatherAdvEl.querySelector('.advisory-icon') : null;

                if (sourceKey === 'meteo' && weatherAdvEl) {
                    weatherAdvEl.style.display = 'block'; 
                    const weatherTextEl = document.getElementById('weather-text');
                    if(weatherTextEl) weatherTextEl.textContent = `Error: ${errorText}`;
                    if(weatherIconDisplayEl) weatherIconDisplayEl.textContent = '⚠️';
                } else if (sourceKey === 'agro' && agroAdvEl) {
                    agroAdvEl.style.display = 'block'; 
                    const agroTextEl = document.getElementById('agro-text');
                    if(agroTextEl) agroTextEl.textContent = `Error: ${errorText}`;
                } else if ((sourceKey === 'cap' || sourceKey === 'gmet_cap') && capDispEl) { 
                    capDispEl.style.display = 'block'; 
                    const capHeadlineEl = document.getElementById('cap-headline');
                    if(capHeadlineEl) capHeadlineEl.textContent = 'CAP Processing Error';
                    const capDescEl = document.getElementById('cap-description');
                    if(capDescEl) capDescEl.textContent = errorText;
                } 
                // Handle error for ghana_meteo specific card if it exists
                else if (sourceKey === 'ghana_meteo' && ghanaMeteoAdvisoryEl) { // Assuming 'ghana_meteo' key for this
                    ghanaMeteoAdvisoryEl.style.display = 'block';
                    const gmetTextEl = document.getElementById('ghana-meteo-text');
                    if (gmetTextEl) gmetTextEl.textContent = `Error: ${errorText}`;
                }
                return; 
            }

            const activeAlertsLegacyEl = document.getElementById('active-alerts-legacy');
            const responseTimeEl = document.getElementById('response-time');
            const weatherIconDisplayEl = weatherAdvEl ? weatherAdvEl.querySelector('.advisory-icon') : null;


            if (sourceKey === 'meteo' && data && data.advisory_text && weatherAdvEl) {
                weatherAdvEl.style.display = 'block'; 
                const weatherTextEl = document.getElementById('weather-text');
                if(weatherTextEl) weatherTextEl.textContent = data.advisory_text;
                if(weatherIconDisplayEl && data.icon_emoji) weatherIconDisplayEl.textContent = data.icon_emoji;
                
                if(activeAlertsLegacyEl) activeAlertsLegacyEl.textContent = data.alerts_count || '1'; 
                if(responseTimeEl) responseTimeEl.textContent = data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : '--';
                
                if (currentWeatherEffect) scene.remove(currentWeatherEffect); 
                currentWeatherEffect = null;
                if (data.weather_code !== undefined) {
                    if (data.weather_code >= 0 && data.weather_code <= 1) currentWeatherEffect = createSunEffect();
                    else if ((data.weather_code >= 51 && data.weather_code <= 67) || data.weather_code >= 80) currentWeatherEffect = createRainEffect();
                    if (currentWeatherEffect) scene.add(currentWeatherEffect);
                }
                if (typeof data.longitude === 'number' && typeof data.latitude === 'number') {
                    let eventTypeForMap = "default"; 
                    if (data.weather_code >= 51) eventTypeForMap = "rain";
                    focusMapOnCAPIncident(data.longitude, data.latitude, eventTypeForMap, 0.6); 
                }
            } else if (sourceKey === 'agro' && data && data.advisory_text && agroAdvEl) {
                agroAdvEl.style.display = 'block'; 
                const agroTextEl = document.getElementById('agro-text');
                if(agroTextEl) agroTextEl.textContent = data.advisory_text;
                if(activeAlertsLegacyEl) activeAlertsLegacyEl.textContent = data.alerts_count || '1';
            
            } else if (sourceKey === 'gmet_cap' && data && data.headline && capDispEl ) { // Using capDisplayEl for gmet_cap too
                capDispEl.style.display = 'block';
                const capHeadlineEl = document.getElementById('cap-headline');
                if(capHeadlineEl) capHeadlineEl.textContent = data.headline;
                const capDescEl = document.getElementById('cap-description');
                if(capDescEl) capDescEl.textContent = data.description;
                const capAreaEl = document.getElementById('cap-area');
                if(capAreaEl) capAreaEl.textContent = `Area: ${data.area_description || data.areaDesc || 'N/A'}`;
                const capSenderEl = document.getElementById('cap-sender');
                if(capSenderEl) capSenderEl.textContent = `Sender: ${data.sender || 'N/A'}`;
                const capSentEl = document.getElementById('cap-sent-time');
                if(capSentEl) capSentEl.textContent = `Sent: ${data.sent_time_cap || (data.sent ? new Date(data.sent).toLocaleString() : 'N/A') }`;
                
                let capGmetCountEl = document.getElementById('cap-gmet-count'); // Check if this specific ID exists
                if (capGmetCountEl) {
                     let gmetCount = parseInt(capGmetCountEl.textContent.split(': ')[1]) || 0;
                     capGmetCountEl.textContent = `GMet: ${gmetCount + 1}`;
                } else { // Fallback to a general meteo count if specific one isn't there
                    let capMeteoCountEl = document.getElementById('cap-meteo-count');
                    if (capMeteoCountEl) {
                        let meteoCapCount = parseInt(capMeteoCountEl.textContent.split(': ')[1]) || 0;
                        capMeteoCountEl.textContent = `Meteo API: ${meteoCapCount + 1}`;
                    }
                }
                const capLongitude = data.longitude; 
                const capLatitude = data.latitude;   
                const eventType = data.event ? data.event.toLowerCase() : "default";
                let mapEventType = "default";
                if (eventType.includes("fire")) mapEventType = "fire";
                else if (eventType.includes("flood") || eventType.includes("inundation")) mapEventType = "flood";
                else if (eventType.includes("rain") || eventType.includes("thunderstorm") || eventType.includes("storm")) mapEventType = "rain";
                if (data.headline !== "CAP Processing Error" && typeof capLongitude === 'number' && typeof capLatitude === 'number') {
                    focusMapOnCAPIncident(capLongitude, capLatitude, mapEventType);
                } else { console.warn("GMet CAP data missing coordinates or is an error, not focusing map.", data); }


            } else if (sourceKey === 'manual_cap_input' && capInArea) { 
                capInArea.style.display = 'block';
            } else if (sourceKey === 'cap' && data && data.headline && capDispEl) { 
                capDispEl.style.display = 'block';
                const capHeadlineEl = document.getElementById('cap-headline');
                if(capHeadlineEl) capHeadlineEl.textContent = data.headline;
                const capDescEl = document.getElementById('cap-description');
                if(capDescEl) capDescEl.textContent = data.description;
                const capAreaEl = document.getElementById('cap-area');
                if(capAreaEl) capAreaEl.textContent = `Area: ${data.area_description || data.areaDesc || 'N/A'}`;
                const capSenderEl = document.getElementById('cap-sender');
                if(capSenderEl) capSenderEl.textContent = `Sender: ${data.sender || 'N/A'}`;
                const capSentEl = document.getElementById('cap-sent-time');
                if(capSentEl) capSentEl.textContent = `Sent: ${data.sent_time_cap ? new Date(data.sent_time_cap).toLocaleString() : 'N/A'}`;
                
                const capManualCountEl = document.getElementById('cap-manual-count');
                if (capManualCountEl) {
                    let manualCount = parseInt(capManualCountEl.textContent.split(': ')[1]) || 0;
                    capManualCountEl.textContent = `Manual: ${manualCount + 1}`;
                }
                
                const capLongitude = data.longitude; 
                const capLatitude = data.latitude;   
                const eventType = data.event ? data.event.toLowerCase() : "default";
                let mapEventType = "default";
                if (eventType.includes("fire")) mapEventType = "fire";
                else if (eventType.includes("flood") || eventType.includes("inundation")) mapEventType = "flood";
                else if (eventType.includes("rain") || eventType.includes("thunderstorm") || eventType.includes("storm")) mapEventType = "rain";

                if (data.headline !== "CAP Processing Error" && typeof capLongitude === 'number' && typeof capLatitude === 'number') {
                    focusMapOnCAPIncident(capLongitude, capLatitude, mapEventType);
                } else { console.warn("Manual CAP data missing coordinates or is an error, not focusing map.", data); }
            }

            // Update total CAP alerts count if any CAP type was processed
            if (sourceKey === 'cap' || sourceKey === 'gmet_cap') {
                 let currentTotalEl = document.getElementById('cap-alerts-total');
                 if(currentTotalEl) {
                    let currentTotal = parseInt(currentTotalEl.textContent) || 0;
                    currentTotalEl.textContent = currentTotal + 1; // Increment, assuming this data represents a new alert
                 }
            }

            // gsap.fromTo('.advisory-card:not([style*="display: none"])', {opacity: 0, y:20}, {opacity:1, y:0, duration:0.5, stagger:0.1});

                // --- The Fix is Here ---
// At the end of the updateAdvisoryUI function:

// 1. Find the card that was (or wasn't) made visible.
const targetCard = document.querySelector('.advisory-card:not([style*="display: none"])');

// 2. ONLY if a visible card was found, run the animation.
if (targetCard) {
    gsap.fromTo(targetCard, {opacity: 0, y: 20}, {opacity: 1, y: 0, duration: 0.5});
}
        }

        function mainAnimateLoop() { 
            requestAnimationFrame(mainAnimateLoop);
            if (ghanaMapGroup && !incidentMarker) { 
                 ghanaMapGroup.rotation.y += 0.0005; 
            }
            if (currentWeatherEffect && currentWeatherEffect.children.length > 0 && 
                currentWeatherEffect.children[0].geometry && 
                currentWeatherEffect.children[0].geometry.attributes && 
                currentWeatherEffect.children[0].geometry.attributes.position) {
                const firstChildIsLine = currentWeatherEffect.children[0].type === 'Line'; 
                 if(firstChildIsLine) { 
                     currentWeatherEffect.children.forEach(line => {
                        if (line.geometry && line.geometry.attributes && line.geometry.attributes.position) {
                            const positions = line.geometry.attributes.position.array;
                            positions[1] -= 0.3; positions[4] -= 0.3; 
                            if (positions[4] < -5) { positions[1] = Math.random() * 15; positions[4] = positions[1] - 0.5; }
                            line.geometry.attributes.position.needsUpdate = true;
                        }
                    });
                 }
            }
            if (currentWeatherEffect && currentWeatherEffect.children.length > 0 && 
                currentWeatherEffect.children[0].type === 'Mesh' && 
                currentWeatherEffect.children.length > 1 && 
                currentWeatherEffect.children[1].type === 'Group') { 
                currentWeatherEffect.rotation.y += 0.01;
            }
            if (renderer && scene && camera) renderer.render(scene, camera); // Check if renderer is initialized
        }
        
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
        
        function initializeVisibility() {
            const glassCards = document.querySelectorAll('.glass-card');
            glassCards.forEach(card => {
                card.style.opacity = '1';
                card.style.visibility = 'visible';
            });
            const weatherAdvisory = document.getElementById('weather-advisory');
            if (weatherAdvisory) {
                weatherAdvisory.style.display = 'block';
                weatherAdvisory.style.opacity = '1';
            }
            const dataSourcesContainer = document.querySelector('.data-sources');
            if (dataSourcesContainer) {
                dataSourcesContainer.style.opacity = '1';
                dataSourcesContainer.style.visibility = 'visible';
            }
            const advisoryPanel = document.querySelector('.advisory-panel');
            if (advisoryPanel) {
                advisoryPanel.style.opacity = '1';
                advisoryPanel.style.visibility = 'visible';
            }
        }
        // --- End of Part 3 ---

        // --- Part 4: DOMContentLoaded - UI Element Initialization, Event Listeners, and Initial Calls ---

        document.addEventListener('DOMContentLoaded', () => {
            // Complete Three.js renderer setup
            const canvasContainer = document.getElementById('canvas-container');
            if (canvasContainer) {
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                canvasContainer.appendChild(renderer.domElement);
            } else {
                console.error("CRITICAL ERROR: 'canvas-container' element not found in the DOM. Map cannot be rendered.");
            }
            
            // Initial Camera Position
            camera.position.set(0, 12, 20); 
            camera.lookAt(0,0,0); 

            // Initialize UI Elements (now that DOM is ready)
            const sourceButtons = document.querySelectorAll('.source-button');
            const capXmlInput = document.getElementById('cap-xml-input');
            const submitCapXmlButton = document.getElementById('submit-cap-xml');
            const capInputArea = document.getElementById('cap-input-area');
            const weatherAdvisoryEl = document.getElementById('weather-advisory');
            const agroAdvisoryEl = document.getElementById('agro-advisory');
            const capDisplayEl = document.getElementById('cap-alert-display');
            const ghanaMeteoAdvisoryEl = document.getElementById('ghana-meteo-advisory'); // If using Untitled-2.html structure

            // Attach Event Listeners to Source Buttons
            if (sourceButtons.length > 0) {
                sourceButtons.forEach(button => { 
                    if (button.disabled) return;
                    button.addEventListener('click', async () => {
                        const source = button.dataset.source;
                        sourceButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        
                        if(weatherAdvisoryEl) weatherAdvisoryEl.style.display = 'none'; 
                        if(agroAdvisoryEl) agroAdvisoryEl.style.display = 'none';
                        if(capDisplayEl) capDisplayEl.style.display = 'none'; 
                        if(capInputArea) capInputArea.style.display = 'none';
                        if(ghanaMeteoAdvisoryEl) ghanaMeteoAdvisoryEl.style.display = 'none';


                        if (source === 'manual_cap') { 
                            updateAdvisoryUI('manual_cap_input', {}); 
                            return; 
                        }
                        
                        let fetchUrl = '';
                        let sourceNameForAnimation = '';

                        if (source === 'meteo') {
                            fetchUrl = `/fetch_data?source=meteo`;
                            sourceNameForAnimation = 'Weather';
                        } else if (source === 'agro') {
                            fetchUrl = `/fetch_data?source=agro`;
                            sourceNameForAnimation = 'Agricultural';
                        } else if (source === 'gmet_cap') { // For GMet RSS alerts
                            fetchUrl = `/fetch_gmet_alerts`;
                            sourceNameForAnimation = 'GMet CAP';
                        } else if (button.id === 'btn-ghana-meteo' && source === 'ghana_meteo') { // For original ghana_meteo button if different
                            fetchUrl = `/fetch_data?source=ghana_meteo`; // Assuming this endpoint exists for it
                            sourceNameForAnimation = 'Ghana Meteo CAP (Legacy)';
                        }
                        else {
                            // Check if the button ID exists for other sources mentioned in Untitled-2.html
                            // This part assumes your HTML has buttons with data-source="nadmo", "fire", "police"
                            // and corresponding btn-nadmo, btn-fire, btn-police IDs if they were meant to be active.
                            // For now, only implemented sources will proceed.
                            const knownImplementedSources = ['meteo', 'agro', 'gmet_cap', 'manual_cap', 'ghana_meteo'];
                            if (!knownImplementedSources.includes(source)) {
                                alert(`Data source "${source}" not yet implemented for fetching.`);
                                return;
                            }
                        }


                        currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                        console.log('Set currentSessionId for new request:', currentSessionId);
                        processAnimation.show(source, `Fetching ${sourceNameForAnimation} Data`);

                        try {
                            const response = await fetch(`${fetchUrl}${fetchUrl.includes('?') ? '&' : '?'}session_id=${currentSessionId}`); 
                            if (!response.ok) {
                                let errorData = { error: `HTTP error ${response.status}`, advisory_text: `Failed to load data. Server responded with ${response.status}`};
                                try { errorData = await response.json(); } catch (e) { /* ignore */ }
                                throw new Error(errorData.advisory_text || errorData.error);
                            }
                            const data = await response.json();
                            updateAdvisoryUI(source, data); 
                        } catch (error) {
                            console.error(`Error fetching ${source} data:`, error);
                            processAnimation.setError(`Fetch Failed: ${error.message.substring(0,100)}`);
                            updateAdvisoryUI(source, { error: error.message, advisory_text: `Failed to load ${source} data.` });
                        }
                    });
                });
            } else {
                console.warn("No elements found with class '.source-button'. Button listeners not attached.");
            }

            // Attach Event Listener to Submit CAP XML Button
            if (submitCapXmlButton && capXmlInput && capInputArea) { 
                submitCapXmlButton.addEventListener('click', async () => {
                    const capXml = capXmlInput.value;
                    if (!capXml.trim()) { alert("Please paste CAP XML data."); return; }
                    capInputArea.style.display = 'none'; 
                    currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); 
                    processAnimation.show('cap', 'Processing Manual CAP Alert'); 
                    try {
                        const response = await fetch(`/submit_cap?session_id=${currentSessionId}`, { 
                            method: 'POST', headers: { 'Content-Type': 'application/xml' }, body: capXml 
                        });
                         if (!response.ok) {
                            let errorData = { error: `HTTP error ${response.status}`, advisory_text: `Failed to process CAP. Server responded with status ${response.status}`};
                            try{ errorData = await response.json(); } catch(e){ /* ignore */ }
                            throw new Error(errorData.advisory_text || errorData.error);
                        }
                        const data = await response.json();
                        updateAdvisoryUI('cap', data); 
                    } catch (error) {
                        console.error('Error submitting CAP XML:', error);
                        processAnimation.setError(`Submit Failed: ${error.message.substring(0,100)}`);
                        updateAdvisoryUI('cap', { error: error.message, advisory_text: 'Failed to process CAP alert.' });
                    } 
                });
            }

            // Attach Event Listener to Test Map Focus Button (if it exists)
            const testFocusMapButton = document.getElementById('test-focus-map');
            if (testFocusMapButton) {
                testFocusMapButton.addEventListener('click', () => {
                    const latInput = document.getElementById('test-lat');
                    const lonInput = document.getElementById('test-lon');
                    const eventInput = document.getElementById('test-event');

                    const lat = latInput ? parseFloat(latInput.value) : NaN;
                    const lon = lonInput ? parseFloat(lonInput.value) : NaN;
                    const event = eventInput ? eventInput.value : 'test';
                    
                    if (!isNaN(lat) && !isNaN(lon)) {
                        focusMapOnCAPIncident(lon, lat, event, 0.5); 
                    } else {
                        alert("Please enter valid latitude and longitude for testing.");
                    }
                });
            }

            // Initial calls
            loadAndCreateGhanaMap(); 
            mainAnimateLoop(); 
            initializeVisibility(); 

            // GSAP animations and final UI setup
            setTimeout(() => {
                const loadingEl = document.getElementById('loading');
                if (loadingEl) loadingEl.classList.add('hidden');
                
                gsap.from('.header h1', { y: -50, opacity: 0, duration: 1, ease: 'power3.out' });
                gsap.from('.glass-card:not(.info-card)', { scale: 0.8, opacity: 0, duration: 0.8, stagger: 0.1, ease: 'power3.out', delay: 0.3 });
                gsap.from('.info-card', { scale: 0.8, opacity: 0, duration: 0.5, stagger: 0.1, ease: 'power3.out', delay: 0.5 });
                
                if(weatherAdvisoryEl) {
                    weatherAdvisoryEl.style.display = 'block';
                    const weatherTextEl = document.getElementById('weather-text');
                    if(weatherTextEl) weatherTextEl.textContent = "Select a data source or input a CAP alert.";
                }
                if(agroAdvisoryEl) agroAdvisoryEl.style.display = 'none';
                if(capDisplayEl) capDisplayEl.style.display = 'none';
                if(capInputArea) capInputArea.style.display = 'none';
                if(ghanaMeteoAdvisoryEl) ghanaMeteoAdvisoryEl.style.display = 'none'; // From Untitled-2.html
            }, 1000); 
        });
        // --- End of Part 4 ---
    </script>
</script>
</body>
</html>

